<?php
require_once '../includes/auth.php';
$auth = requireRole('admin');
$user = $auth->getCurrentUser();

$db = getDB();
$message = '';
$error = '';

// معالجة إنشاء مهمة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_task'])) {
    $title = sanitize($_POST['title']);
    $description = sanitize($_POST['description']);
    $assigned_to = (int)$_POST['assigned_to'];
    $supervisor_id = !empty($_POST['supervisor_id']) ? (int)$_POST['supervisor_id'] : null;
    $priority = sanitize($_POST['priority']);
    $due_date = $_POST['due_date'];
    $estimated_hours = !empty($_POST['estimated_hours']) ? (float)$_POST['estimated_hours'] : null;
    
    if (empty($title) || empty($description) || empty($assigned_to) || empty($due_date)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        try {
            $stmt = $db->prepare("INSERT INTO tasks (title, description, assigned_by, assigned_to, supervisor_id, priority, due_date, estimated_hours) 
                                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$title, $description, $user['id'], $assigned_to, $supervisor_id, $priority, $due_date, $estimated_hours]);
            
            // إنشاء إشعار للموظف المكلف
            $task_id = $db->lastInsertId();
            $stmt = $db->prepare("INSERT INTO notifications (user_id, task_id, title, message, type) 
                                 VALUES (?, ?, ?, ?, 'task_assigned')");
            $stmt->execute([$assigned_to, $task_id, 'مهمة جديدة', "تم تكليفك بمهمة جديدة: $title"]);
            
            $message = 'تم إنشاء المهمة بنجاح';
        } catch (Exception $e) {
            $error = 'حدث خطأ أثناء إنشاء المهمة';
        }
    }
}

// الحصول على قائمة المهام
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$priority_filter = isset($_GET['priority']) ? sanitize($_GET['priority']) : '';

$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(t.title LIKE ? OR t.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "t.status = ?";
    $params[] = $status_filter;
}

if (!empty($priority_filter)) {
    $where_conditions[] = "t.priority = ?";
    $params[] = $priority_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

$stmt = $db->prepare("SELECT t.*, u1.full_name as assigned_by_name, u2.full_name as assigned_to_name, 
                     u3.full_name as supervisor_name, d.name as department_name
                     FROM tasks t 
                     JOIN users u1 ON t.assigned_by = u1.id 
                     JOIN users u2 ON t.assigned_to = u2.id 
                     LEFT JOIN users u3 ON t.supervisor_id = u3.id 
                     LEFT JOIN departments d ON t.department_id = d.id 
                     $where_clause
                     ORDER BY t.created_at DESC");
$stmt->execute($params);
$tasks = $stmt->fetchAll();

// الحصول على قائمة الموظفين
$stmt = $db->query("SELECT id, full_name, role FROM users WHERE is_active = 1 ORDER BY full_name");
$users = $stmt->fetchAll();

// الحصول على قائمة المشرفين
$stmt = $db->query("SELECT id, full_name FROM users WHERE role = 'supervisor' AND is_active = 1 ORDER BY full_name");
$supervisors = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المهام - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">لوحة المدير</h5>
                        <small class="text-light"><?php echo $user['full_name']; ?></small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="tasks.php">
                                <i class="fas fa-tasks"></i>
                                إدارة المهام
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="departments.php">
                                <i class="fas fa-building"></i>
                                الأقسام
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="../includes/logout.php">
                                <i class="fas fa-sign-out-alt"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة المهام</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newTaskModal">
                            <i class="fas fa-plus"></i> مهمة جديدة
                        </button>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $error; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="<?php echo htmlspecialchars($search); ?>" placeholder="البحث في المهام...">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" <?php echo $status_filter == 'pending' ? 'selected' : ''; ?>>معلقة</option>
                                    <option value="in_progress" <?php echo $status_filter == 'in_progress' ? 'selected' : ''; ?>>قيد التنفيذ</option>
                                    <option value="completed" <?php echo $status_filter == 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                                    <option value="overdue" <?php echo $status_filter == 'overdue' ? 'selected' : ''; ?>>متأخرة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="priority" class="form-label">الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="">جميع الأولويات</option>
                                    <option value="low" <?php echo $priority_filter == 'low' ? 'selected' : ''; ?>>منخفضة</option>
                                    <option value="medium" <?php echo $priority_filter == 'medium' ? 'selected' : ''; ?>>متوسطة</option>
                                    <option value="high" <?php echo $priority_filter == 'high' ? 'selected' : ''; ?>>عالية</option>
                                    <option value="urgent" <?php echo $priority_filter == 'urgent' ? 'selected' : ''; ?>>عاجلة</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- جدول المهام -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list me-2"></i>قائمة المهام (<?php echo count($tasks); ?>)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>المهمة</th>
                                        <th>المكلف</th>
                                        <th>المشرف</th>
                                        <th>الأولوية</th>
                                        <th>الحالة</th>
                                        <th>التقدم</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tasks as $task): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($task['title']); ?></strong>
                                            <br>
                                            <small class="text-muted"><?php echo substr(htmlspecialchars($task['description']), 0, 50) . '...'; ?></small>
                                        </td>
                                        <td><?php echo htmlspecialchars($task['assigned_to_name']); ?></td>
                                        <td><?php echo $task['supervisor_name'] ? htmlspecialchars($task['supervisor_name']) : '-'; ?></td>
                                        <td>
                                            <?php
                                            $priority_class = '';
                                            $priority_text = '';
                                            switch ($task['priority']) {
                                                case 'low':
                                                    $priority_class = 'bg-secondary';
                                                    $priority_text = 'منخفضة';
                                                    break;
                                                case 'medium':
                                                    $priority_class = 'bg-info';
                                                    $priority_text = 'متوسطة';
                                                    break;
                                                case 'high':
                                                    $priority_class = 'bg-warning';
                                                    $priority_text = 'عالية';
                                                    break;
                                                case 'urgent':
                                                    $priority_class = 'bg-danger';
                                                    $priority_text = 'عاجلة';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $priority_class; ?>"><?php echo $priority_text; ?></span>
                                        </td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            $status_text = '';
                                            switch ($task['status']) {
                                                case 'pending':
                                                    $status_class = 'badge-pending';
                                                    $status_text = 'معلقة';
                                                    break;
                                                case 'in_progress':
                                                    $status_class = 'badge-in-progress';
                                                    $status_text = 'قيد التنفيذ';
                                                    break;
                                                case 'completed':
                                                    $status_class = 'badge-completed';
                                                    $status_text = 'مكتملة';
                                                    break;
                                                case 'overdue':
                                                    $status_class = 'badge-overdue';
                                                    $status_text = 'متأخرة';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: <?php echo $task['progress_percentage']; ?>%"
                                                     aria-valuenow="<?php echo $task['progress_percentage']; ?>" 
                                                     aria-valuemin="0" aria-valuemax="100">
                                                    <?php echo $task['progress_percentage']; ?>%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php echo formatArabicDate($task['due_date']); ?>
                                            <br>
                                            <small class="text-muted"><?php echo timeRemaining($task['due_date']); ?></small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="task_details.php?id=<?php echo $task['id']; ?>" 
                                                   class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_task.php?id=<?php echo $task['id']; ?>" 
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteTask(<?php echo $task['id']; ?>)" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- نموذج إنشاء مهمة جديدة -->
    <div class="modal fade" id="newTaskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إنشاء مهمة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="title" class="form-label">عنوان المهمة *</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            
                            <div class="col-md-12 mb-3">
                                <label for="description" class="form-label">وصف المهمة *</label>
                                <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="assigned_to" class="form-label">المكلف بالمهمة *</label>
                                <select class="form-select" id="assigned_to" name="assigned_to" required>
                                    <option value="">اختر الموظف</option>
                                    <?php foreach ($users as $emp): ?>
                                        <?php if ($emp['role'] != 'admin'): ?>
                                            <option value="<?php echo $emp['id']; ?>">
                                                <?php echo htmlspecialchars($emp['full_name']) . ' (' . $emp['role'] . ')'; ?>
                                            </option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="supervisor_id" class="form-label">المشرف</label>
                                <select class="form-select" id="supervisor_id" name="supervisor_id">
                                    <option value="">اختر المشرف (اختياري)</option>
                                    <?php foreach ($supervisors as $supervisor): ?>
                                        <option value="<?php echo $supervisor['id']; ?>">
                                            <?php echo htmlspecialchars($supervisor['full_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="priority" class="form-label">الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low">منخفضة</option>
                                    <option value="medium" selected>متوسطة</option>
                                    <option value="high">عالية</option>
                                    <option value="urgent">عاجلة</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="due_date" class="form-label">تاريخ الاستحقاق *</label>
                                <input type="date" class="form-control" id="due_date" name="due_date" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="estimated_hours" class="form-label">الساعات المقدرة</label>
                                <input type="number" class="form-control" id="estimated_hours" name="estimated_hours" 
                                       step="0.5" min="0" placeholder="مثال: 8">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="create_task" class="btn btn-primary">إنشاء المهمة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعيين الحد الأدنى لتاريخ الاستحقاق (اليوم)
        document.getElementById('due_date').min = new Date().toISOString().split('T')[0];
        
        // دالة حذف المهمة
        function deleteTask(taskId) {
            if (confirm('هل أنت متأكد من حذف هذه المهمة؟')) {
                window.location.href = 'delete_task.php?id=' + taskId;
            }
        }
    </script>
</body>
</html>
