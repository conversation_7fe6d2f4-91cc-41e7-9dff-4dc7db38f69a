<?php
require_once 'includes/auth.php';

// إذا كان المستخدم مسجل دخول بالفعل، إعادة توجيه
$auth = new Auth();
if ($auth->isLoggedIn()) {
    $auth->redirectByRole();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        if ($auth->login($username, $password)) {
            $auth->redirectByRole();
        } else {
            $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body class="login-page">
    <div class="container-fluid vh-100">
        <div class="row h-100">
            <!-- الجانب الأيسر - معلومات النظام -->
            <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary">
                <div class="text-center text-white">
                    <i class="fas fa-tasks fa-5x mb-4"></i>
                    <h2 class="mb-3">نظام إدارة المهام</h2>
                    <p class="lead">نظام شامل لإدارة المهام والمشاريع في الشركة</p>
                    <div class="row mt-5">
                        <div class="col-4">
                            <i class="fas fa-user-tie fa-2x mb-2"></i>
                            <h6>إدارة شاملة</h6>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <h6>تقارير مفصلة</h6>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-bell fa-2x mb-2"></i>
                            <h6>إشعارات فورية</h6>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
            <div class="col-lg-6 d-flex align-items-center justify-content-center">
                <div class="login-form-container">
                    <div class="card shadow-lg border-0">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                <i class="fas fa-user-circle fa-3x text-primary mb-3"></i>
                                <h3 class="card-title">تسجيل الدخول</h3>
                                <p class="text-muted">أدخل بياناتك للوصول إلى النظام</p>
                            </div>
                            
                            <?php if ($error): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo $error; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo $success; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="">
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-2"></i>اسم المستخدم
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="username" 
                                           name="username" required value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                                </div>
                                
                                <div class="mb-4">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>كلمة المرور
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control form-control-lg" id="password" 
                                               name="password" required>
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                    </button>
                                </div>
                            </form>
                            
                            <div class="mt-4 text-center">
                                <small class="text-muted">
                                    للحصول على حساب جديد، تواصل مع مدير النظام
                                </small>
                            </div>
                            
                            <!-- معلومات الحسابات التجريبية -->
                            <div class="mt-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">حسابات تجريبية:</h6>
                                        <small class="text-muted">
                                            <strong>مدير:</strong> admin / password<br>
                                            <strong>مشرف:</strong> supervisor1 / password<br>
                                            <strong>موظف:</strong> employee1 / password
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // إظهار/إخفاء كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    </script>
</body>
</html>
