<?php
/**
 * نظام المصادقة والجلسات
 * نظام إدارة المهام
 */

session_start();
require_once '../config/database.php';

class Auth {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    // تسجيل الدخول
    public function login($username, $password) {
        try {
            $stmt = $this->db->prepare("SELECT * FROM users WHERE username = ? AND is_active = 1");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if ($user && verifyPassword($password, $user['password'])) {
                // تحديث آخر تسجيل دخول
                $this->updateLastLogin($user['id']);
                
                // إنشاء الجلسة
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['department_id'] = $user['department_id'];
                $_SESSION['last_activity'] = time();
                
                return true;
            }
            return false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    // تسجيل الخروج
    public function logout() {
        session_unset();
        session_destroy();
        header("Location: ../login.php");
        exit();
    }
    
    // التحقق من تسجيل الدخول
    public function isLoggedIn() {
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // التحقق من انتهاء الجلسة
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
            $this->logout();
            return false;
        }
        
        $_SESSION['last_activity'] = time();
        return true;
    }
    
    // التحقق من الصلاحية
    public function hasRole($role) {
        return isset($_SESSION['role']) && $_SESSION['role'] === $role;
    }
    
    // التحقق من صلاحيات متعددة
    public function hasAnyRole($roles) {
        return isset($_SESSION['role']) && in_array($_SESSION['role'], $roles);
    }
    
    // الحصول على معلومات المستخدم الحالي
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        try {
            $stmt = $this->db->prepare("SELECT u.*, d.name as department_name FROM users u 
                                      LEFT JOIN departments d ON u.department_id = d.id 
                                      WHERE u.id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }
    
    // تحديث آخر تسجيل دخول
    private function updateLastLogin($user_id) {
        try {
            $stmt = $this->db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $stmt->execute([$user_id]);
        } catch (Exception $e) {
            // تجاهل الخطأ
        }
    }
    
    // إعادة توجيه حسب الدور
    public function redirectByRole() {
        if (!$this->isLoggedIn()) {
            header("Location: ../login.php");
            exit();
        }
        
        switch ($_SESSION['role']) {
            case 'admin':
                header("Location: ../admin/dashboard.php");
                break;
            case 'supervisor':
                header("Location: ../supervisor/dashboard.php");
                break;
            case 'employee':
                header("Location: ../employee/dashboard.php");
                break;
            default:
                $this->logout();
        }
        exit();
    }
}

// دالة للتحقق من تسجيل الدخول (للاستخدام في الصفحات)
function requireLogin() {
    $auth = new Auth();
    if (!$auth->isLoggedIn()) {
        header("Location: ../login.php");
        exit();
    }
    return $auth;
}

// دالة للتحقق من الصلاحية (للاستخدام في الصفحات)
function requireRole($role) {
    $auth = requireLogin();
    if (!$auth->hasRole($role)) {
        header("Location: ../access_denied.php");
        exit();
    }
    return $auth;
}

// دالة للتحقق من صلاحيات متعددة
function requireAnyRole($roles) {
    $auth = requireLogin();
    if (!$auth->hasAnyRole($roles)) {
        header("Location: ../access_denied.php");
        exit();
    }
    return $auth;
}
?>
