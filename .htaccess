# نظام إدارة المهام - إعدادات Apache

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "config/database.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "includes/auth.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

<Files "README.md">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول لمجلد SQL
<Directory "sql">
    Order Allow,Deny
    Deny from all
</Directory>

# إعدادات الأمان
ServerSignature Off

# منع عرض محتويات المجلدات
Options -Indexes

# حماية من هجمات XSS
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType application/x-icon "access plus 1 month"
</IfModule>

# إعادة توجيه الصفحة الرئيسية
DirectoryIndex index.php

# صفحات الأخطاء المخصصة
ErrorDocument 404 /JOMK/404.php
ErrorDocument 403 /JOMK/access_denied.php
ErrorDocument 500 /JOMK/500.php

# منع الوصول للملفات المخفية
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# حد أقصى لحجم الملف المرفوع (5MB)
php_value upload_max_filesize 5M
php_value post_max_size 5M

# إعدادات الجلسة
php_value session.cookie_httponly 1
php_value session.cookie_secure 0
php_value session.use_only_cookies 1
