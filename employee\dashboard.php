<?php
require_once '../includes/auth.php';
$auth = requireRole('employee');
$user = $auth->getCurrentUser();

$db = getDB();

// الحصول على إحصائيات مهام الموظف
$stmt = $db->prepare("SELECT 
    COUNT(*) as total_tasks,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_tasks,
    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tasks,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
    SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdue_tasks
    FROM tasks WHERE assigned_to = ?");
$stmt->execute([$user['id']]);
$task_stats = $stmt->fetch();

// الحصول على المهام الحديثة للموظف
$stmt = $db->prepare("SELECT t.*, u1.full_name as assigned_by_name 
                     FROM tasks t 
                     JOIN users u1 ON t.assigned_by = u1.id 
                     WHERE t.assigned_to = ? 
                     ORDER BY t.created_at DESC LIMIT 5");
$stmt->execute([$user['id']]);
$recent_tasks = $stmt->fetchAll();

// الحصول على الإشعارات غير المقروءة
$stmt = $db->prepare("SELECT COUNT(*) as unread_count FROM notifications WHERE user_id = ? AND is_read = 0");
$stmt->execute([$user['id']]);
$unread_notifications = $stmt->fetch()['unread_count'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الموظف - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <img src="../assets/images/avatar.png" alt="Avatar" class="rounded-circle mb-2" style="width: 60px; height: 60px;" onerror="this.style.display='none'">
                        <h6 class="text-white">مرحباً</h6>
                        <small class="text-light"><?php echo $user['full_name']; ?></small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="my_tasks.php">
                                <i class="fas fa-tasks"></i>
                                مهامي
                                <?php if ($task_stats['pending_tasks'] > 0): ?>
                                    <span class="badge bg-warning ms-2"><?php echo $task_stats['pending_tasks']; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notifications.php">
                                <i class="fas fa-bell"></i>
                                الإشعارات
                                <?php if ($unread_notifications > 0): ?>
                                    <span class="badge bg-danger ms-2"><?php echo $unread_notifications; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="profile.php">
                                <i class="fas fa-user"></i>
                                الملف الشخصي
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="../includes/logout.php">
                                <i class="fas fa-sign-out-alt"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- الهيدر -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">مرحباً، <?php echo $user['full_name']; ?></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <span class="badge bg-info fs-6">
                                <i class="fas fa-building me-1"></i>
                                <?php echo $user['department_name'] ?: 'غير محدد'; ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="stats-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="stats-number"><?php echo $task_stats['total_tasks']; ?></div>
                            <div class="stats-label">إجمالي المهام</div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card" style="background: linear-gradient(135deg, #f39c12 0%, #d68910 100%);">
                            <div class="stats-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stats-number"><?php echo $task_stats['pending_tasks']; ?></div>
                            <div class="stats-label">مهام معلقة</div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
                            <div class="stats-icon">
                                <i class="fas fa-spinner"></i>
                            </div>
                            <div class="stats-number"><?php echo $task_stats['in_progress_tasks']; ?></div>
                            <div class="stats-label">قيد التنفيذ</div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                            <div class="stats-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stats-number"><?php echo $task_stats['completed_tasks']; ?></div>
                            <div class="stats-label">مكتملة</div>
                        </div>
                    </div>
                </div>

                <!-- المهام الحديثة -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list me-2"></i>مهامي الحديثة</h5>
                            </div>
                            <div class="card-body">
                                <?php if (empty($recent_tasks)): ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">لا توجد مهام حالياً</h5>
                                        <p class="text-muted">ستظهر المهام المكلف بها هنا</p>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>المهمة</th>
                                                    <th>المكلف من</th>
                                                    <th>الحالة</th>
                                                    <th>التقدم</th>
                                                    <th>تاريخ الاستحقاق</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recent_tasks as $task): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($task['title']); ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo substr(htmlspecialchars($task['description']), 0, 50) . '...'; ?></small>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($task['assigned_by_name']); ?></td>
                                                    <td>
                                                        <?php
                                                        $status_class = '';
                                                        $status_text = '';
                                                        switch ($task['status']) {
                                                            case 'pending':
                                                                $status_class = 'badge-pending';
                                                                $status_text = 'معلقة';
                                                                break;
                                                            case 'in_progress':
                                                                $status_class = 'badge-in-progress';
                                                                $status_text = 'قيد التنفيذ';
                                                                break;
                                                            case 'completed':
                                                                $status_class = 'badge-completed';
                                                                $status_text = 'مكتملة';
                                                                break;
                                                            case 'overdue':
                                                                $status_class = 'badge-overdue';
                                                                $status_text = 'متأخرة';
                                                                break;
                                                        }
                                                        ?>
                                                        <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="progress" style="height: 20px;">
                                                            <div class="progress-bar" role="progressbar" 
                                                                 style="width: <?php echo $task['progress_percentage']; ?>%"
                                                                 aria-valuenow="<?php echo $task['progress_percentage']; ?>" 
                                                                 aria-valuemin="0" aria-valuemax="100">
                                                                <?php echo $task['progress_percentage']; ?>%
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php echo formatArabicDate($task['due_date']); ?>
                                                        <br>
                                                        <small class="text-muted"><?php echo timeRemaining($task['due_date']); ?></small>
                                                    </td>
                                                    <td>
                                                        <a href="task_details.php?id=<?php echo $task['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i> عرض
                                                        </a>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-center mt-3">
                                        <a href="my_tasks.php" class="btn btn-primary">
                                            <i class="fas fa-list me-2"></i>عرض جميع المهام
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <!-- معلومات سريعة -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle me-2"></i>معلومات سريعة</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>القسم:</strong>
                                    <span class="text-muted"><?php echo $user['department_name'] ?: 'غير محدد'; ?></span>
                                </div>
                                <div class="mb-3">
                                    <strong>البريد الإلكتروني:</strong>
                                    <span class="text-muted"><?php echo htmlspecialchars($user['email']); ?></span>
                                </div>
                                <div class="mb-3">
                                    <strong>آخر تسجيل دخول:</strong>
                                    <span class="text-muted">
                                        <?php echo $user['last_login'] ? formatArabicDate($user['last_login']) : 'لم يسجل من قبل'; ?>
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <strong>تاريخ الانضمام:</strong>
                                    <span class="text-muted"><?php echo formatArabicDate($user['created_at']); ?></span>
                                </div>
                            </div>
                        </div>

                        <!-- نصائح سريعة -->
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-lightbulb me-2"></i>نصائح سريعة</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        حدث حالة مهامك بانتظام
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-clock text-warning me-2"></i>
                                        راقب مواعيد الاستحقاق
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-comments text-info me-2"></i>
                                        تواصل مع المشرف عند الحاجة
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-bell text-primary me-2"></i>
                                        تابع الإشعارات الجديدة
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
