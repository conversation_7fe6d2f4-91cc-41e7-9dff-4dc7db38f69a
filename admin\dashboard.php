<?php
require_once '../includes/auth.php';
$auth = requireRole('admin');
$user = $auth->getCurrentUser();

// الحصول على الإحصائيات
$db = getDB();

// إحصائيات المهام
$stmt = $db->query("SELECT 
    COUNT(*) as total_tasks,
    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_tasks,
    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_tasks,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
    SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdue_tasks
    FROM tasks");
$task_stats = $stmt->fetch();

// إحصائيات المستخدمين
$stmt = $db->query("SELECT 
    COUNT(*) as total_users,
    <PERSON>UM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_count,
    <PERSON><PERSON>(CASE WHEN role = 'supervisor' THEN 1 ELSE 0 END) as supervisor_count,
    SUM(CASE WHEN role = 'employee' THEN 1 ELSE 0 END) as employee_count
    FROM users WHERE is_active = 1");
$user_stats = $stmt->fetch();

// المهام الحديثة
$stmt = $db->prepare("SELECT t.*, u1.full_name as assigned_by_name, u2.full_name as assigned_to_name 
                     FROM tasks t 
                     JOIN users u1 ON t.assigned_by = u1.id 
                     JOIN users u2 ON t.assigned_to = u2.id 
                     ORDER BY t.created_at DESC LIMIT 5");
$stmt->execute();
$recent_tasks = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <img src="../assets/images/logo.png" alt="Logo" class="img-fluid mb-2" style="max-width: 80px;" onerror="this.style.display='none'">
                        <h5 class="text-white">لوحة المدير</h5>
                        <small class="text-light"><?php echo $user['full_name']; ?></small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="tasks.php">
                                <i class="fas fa-tasks"></i>
                                إدارة المهام
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="departments.php">
                                <i class="fas fa-building"></i>
                                الأقسام
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="../includes/logout.php">
                                <i class="fas fa-sign-out-alt"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- الهيدر -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">لوحة تحكم المدير</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newTaskModal">
                                <i class="fas fa-plus"></i> مهمة جديدة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card">
                            <div class="stats-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="stats-number"><?php echo $task_stats['total_tasks']; ?></div>
                            <div class="stats-label">إجمالي المهام</div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card" style="background: linear-gradient(135deg, #f39c12 0%, #d68910 100%);">
                            <div class="stats-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stats-number"><?php echo $task_stats['pending_tasks']; ?></div>
                            <div class="stats-label">مهام معلقة</div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
                            <div class="stats-icon">
                                <i class="fas fa-spinner"></i>
                            </div>
                            <div class="stats-number"><?php echo $task_stats['in_progress_tasks']; ?></div>
                            <div class="stats-label">قيد التنفيذ</div>
                        </div>
                    </div>
                    
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stats-card" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                            <div class="stats-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stats-number"><?php echo $task_stats['completed_tasks']; ?></div>
                            <div class="stats-label">مكتملة</div>
                        </div>
                    </div>
                </div>

                <!-- المهام الحديثة -->
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list me-2"></i>المهام الحديثة</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>المهمة</th>
                                                <th>المكلف</th>
                                                <th>الحالة</th>
                                                <th>تاريخ الاستحقاق</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_tasks as $task): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($task['title']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo substr(htmlspecialchars($task['description']), 0, 50) . '...'; ?></small>
                                                </td>
                                                <td><?php echo htmlspecialchars($task['assigned_to_name']); ?></td>
                                                <td>
                                                    <?php
                                                    $status_class = '';
                                                    $status_text = '';
                                                    switch ($task['status']) {
                                                        case 'pending':
                                                            $status_class = 'badge-pending';
                                                            $status_text = 'معلقة';
                                                            break;
                                                        case 'in_progress':
                                                            $status_class = 'badge-in-progress';
                                                            $status_text = 'قيد التنفيذ';
                                                            break;
                                                        case 'completed':
                                                            $status_class = 'badge-completed';
                                                            $status_text = 'مكتملة';
                                                            break;
                                                        case 'overdue':
                                                            $status_class = 'badge-overdue';
                                                            $status_text = 'متأخرة';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                                </td>
                                                <td><?php echo formatArabicDate($task['due_date']); ?></td>
                                                <td>
                                                    <a href="task_details.php?id=<?php echo $task['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-users me-2"></i>إحصائيات المستخدمين</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>المديرين</span>
                                        <span class="badge bg-primary"><?php echo $user_stats['admin_count']; ?></span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>المشرفين</span>
                                        <span class="badge bg-info"><?php echo $user_stats['supervisor_count']; ?></span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>الموظفين</span>
                                        <span class="badge bg-success"><?php echo $user_stats['employee_count']; ?></span>
                                    </div>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <strong>الإجمالي</strong>
                                    <strong class="badge bg-dark"><?php echo $user_stats['total_users']; ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
