/* نظام إدارة المهام - ملف التصميم الرئيسي */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* المتغيرات */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* الإعدادات العامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f5f6fa;
    color: #2c3e50;
    line-height: 1.6;
}

/* صفحة تسجيل الدخول */
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.login-form-container {
    max-width: 450px;
    width: 100%;
}

.login-form-container .card {
    border-radius: 15px;
    overflow: hidden;
}

.login-form-container .form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.login-form-container .form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* الشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, var(--primary-color) 0%, #34495e 100%);
    min-height: 100vh;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar .nav-link {
    color: #bdc3c7;
    padding: 12px 20px;
    margin: 5px 10px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255,255,255,0.1);
    color: #fff;
    transform: translateX(-5px);
}

.sidebar .nav-link i {
    width: 20px;
    margin-left: 10px;
}

/* الهيدر */
.main-header {
    background: #fff;
    box-shadow: var(--box-shadow);
    border-bottom: 1px solid #e9ecef;
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color) !important;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #34495e 100%);
    color: #fff;
    border-bottom: none;
    padding: 20px;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 10px 20px;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #2980b9 100%);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9 0%, var(--secondary-color) 100%);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #229954 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d68910 100%);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
}

/* الجداول */
.table {
    background: #fff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: var(--primary-color);
    color: #fff;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table tbody td {
    padding: 15px;
    vertical-align: middle;
    border-top: 1px solid #e9ecef;
}

/* شارات الحالة */
.badge {
    font-size: 0.8em;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 600;
}

.badge-pending {
    background: linear-gradient(135deg, #f39c12 0%, #d68910 100%);
    color: #fff;
}

.badge-in-progress {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: #fff;
}

.badge-completed {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    color: #fff;
}

.badge-overdue {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: #fff;
}

/* شريط التقدم */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    transition: var(--transition);
}

/* الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.stats-card .stats-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.stats-card .stats-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
}

/* الإشعارات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        right: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: var(--transition);
    }
    
    .sidebar.show {
        right: 0;
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .stats-card {
        margin-bottom: 20px;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* تأثيرات إضافية */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* تخصيص السكرول */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #34495e;
}
