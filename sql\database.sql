-- قاعدة بيانات نظام إدارة المهام
CREATE DATABASE IF NOT EXISTS task_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE task_management_system;

-- جد<PERSON><PERSON> الأقسام
CREATE TABLE departments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHA<PERSON>(100) NOT NULL,
    role ENUM('admin', 'supervisor', 'employee') NOT NULL,
    department_id INT,
    phone VARCHAR(20),
    avatar VARCHAR(255),
    is_active BO<PERSON>EAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- جدول المهام
CREATE TABLE tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    assigned_by INT NOT NULL,
    assigned_to INT NOT NULL,
    supervisor_id INT,
    department_id INT,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled', 'overdue') DEFAULT 'pending',
    start_date DATE,
    due_date DATE NOT NULL,
    completion_date TIMESTAMP NULL,
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2),
    progress_percentage INT DEFAULT 0,
    attachments TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (supervisor_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- جدول تحديثات المهام
CREATE TABLE task_updates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    user_id INT NOT NULL,
    update_type ENUM('status_change', 'progress_update', 'comment', 'file_upload') NOT NULL,
    old_value VARCHAR(100),
    new_value VARCHAR(100),
    comment TEXT,
    attachment VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    task_id INT,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('task_assigned', 'task_completed', 'task_overdue', 'task_updated', 'general') NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE
);

-- إدراج بيانات تجريبية
INSERT INTO departments (name, description) VALUES
('تقنية المعلومات', 'قسم تطوير وصيانة الأنظمة التقنية'),
('الموارد البشرية', 'قسم إدارة شؤون الموظفين'),
('المالية', 'قسم الحسابات والشؤون المالية'),
('التسويق', 'قسم التسويق والمبيعات');

-- إدراج مستخدم أدمن افتراضي (كلمة المرور: password)
INSERT INTO users (username, email, password, full_name, role, department_id) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', 1);

-- إدراج مشرف تجريبي (كلمة المرور: password)
INSERT INTO users (username, email, password, full_name, role, department_id) VALUES
('supervisor1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد محمد - مشرف', 'supervisor', 1);

-- إدراج موظف تجريبي (كلمة المرور: password)
INSERT INTO users (username, email, password, full_name, role, department_id) VALUES
('employee1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'سارة أحمد - موظفة', 'employee', 1);
