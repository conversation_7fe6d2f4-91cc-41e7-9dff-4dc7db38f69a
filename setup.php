<?php
/**
 * ملف إعداد النظام
 * يقوم بإنشاء قاعدة البيانات والجداول تلقائياً
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'task_management_system';

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>بدء إعداد نظام إدارة المهام</h2>";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ تم إنشاء قاعدة البيانات: $database</p>";
    
    // الاتصال بقاعدة البيانات المحددة
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // قراءة وتنفيذ ملف SQL
    $sql_file = 'sql/database.sql';
    if (file_exists($sql_file)) {
        $sql = file_get_contents($sql_file);
        
        // تقسيم الاستعلامات
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // تجاهل الأخطاء المتعلقة بالجداول الموجودة
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "<p>⚠️ خطأ في تنفيذ: " . htmlspecialchars($statement) . "</p>";
                        echo "<p>الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
                    }
                }
            }
        }
        
        echo "<p>✅ تم تنفيذ ملف SQL بنجاح</p>";
    } else {
        echo "<p>❌ لم يتم العثور على ملف SQL</p>";
    }
    
    // التحقق من وجود المستخدم الافتراضي
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = 'admin'");
    $stmt->execute();
    $admin_exists = $stmt->fetchColumn();
    
    if ($admin_exists == 0) {
        // إنشاء مستخدم أدمن افتراضي
        $admin_password = password_hash('password', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, full_name, role, department_id) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['admin', '<EMAIL>', $admin_password, 'مدير النظام', 'admin', 1]);
        echo "<p>✅ تم إنشاء حساب المدير الافتراضي</p>";
    }
    
    echo "<h3>🎉 تم إعداد النظام بنجاح!</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>معلومات تسجيل الدخول:</h4>";
    echo "<p><strong>مدير النظام:</strong><br>";
    echo "اسم المستخدم: admin<br>";
    echo "كلمة المرور: password</p>";
    echo "</div>";
    
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب لصفحة تسجيل الدخول</a></p>";
    
} catch (PDOException $e) {
    echo "<h3>❌ خطأ في الإعداد</h3>";
    echo "<p>الخطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>يرجى التأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "<li>وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام إدارة المهام</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        h2, h3 {
            color: #2c3e50;
        }
        p {
            line-height: 1.6;
        }
        .success {
            color: #27ae60;
        }
        .error {
            color: #e74c3c;
        }
        .warning {
            color: #f39c12;
        }
    </style>
</head>
<body>
    <!-- المحتوى يتم عرضه من PHP أعلاه -->
</body>
</html>
