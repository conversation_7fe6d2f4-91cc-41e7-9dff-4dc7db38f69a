# نظام إدارة المهام للشركة

نظام شامل ومتطور لإدارة المهام والمشاريع في الشركة، مصمم بلغة PHP و HTML مع قاعدة بيانات MySQL.

## المميزات الرئيسية

### 🔐 نظام الأدوار والصلاحيات
- **المدير (Admin)**: صلاحية كاملة لإدارة النظام وإنشاء المهام
- **المشرف (Supervisor)**: متابعة المهام وإدارة الفريق
- **الموظف (Employee)**: استقبال وتنفيذ المهام

### 📋 إدارة المهام
- إنشاء وتوزيع المهام على الموظفين
- تحديد الأولويات والمواعيد النهائية
- متابعة تقدم المهام بالنسبة المئوية
- نظام إشعارات تلقائي

### 📊 التقارير والإحصائيات
- لوحات تحكم تفاعلية
- إحصائيات شاملة للأداء
- تقارير مفصلة حسب القسم والموظف

### 🎨 تصميم احترافي
- واجهة مستخدم عصرية ومتجاوبة
- دعم كامل للغة العربية
- تصميم متوافق مع جميع الأجهزة

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx Web Server
- XAMPP/WAMP (للتطوير المحلي)

## تعليمات التثبيت

### 1. إعداد البيئة المحلية

```bash
# تأكد من تشغيل XAMPP
# ابدأ Apache و MySQL من لوحة تحكم XAMPP
```

### 2. نسخ الملفات

```bash
# انسخ جميع ملفات المشروع إلى مجلد htdocs
# المسار: C:\xampp\htdocs\JOMK\
```

### 3. إنشاء قاعدة البيانات

1. افتح phpMyAdmin من خلال: `http://localhost/phpmyadmin`
2. أنشئ قاعدة بيانات جديدة باسم: `task_management_system`
3. استورد ملف SQL من: `sql/database.sql`

### 4. تكوين الإعدادات

عدّل ملف `config/database.php` حسب إعدادات قاعدة البيانات لديك:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'task_management_system');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### 5. الوصول للنظام

افتح المتصفح وانتقل إلى: `http://localhost/JOMK`

## الحسابات الافتراضية

### مدير النظام
- **اسم المستخدم**: admin
- **كلمة المرور**: password

### مشرف
- **اسم المستخدم**: supervisor1
- **كلمة المرور**: password

### موظف
- **اسم المستخدم**: employee1
- **كلمة المرور**: password

## هيكل المشروع

```
JOMK/
├── admin/                  # لوحة تحكم المدير
│   ├── dashboard.php
│   ├── tasks.php
│   └── users.php
├── supervisor/             # لوحة تحكم المشرف
│   ├── dashboard.php
│   └── supervised_tasks.php
├── employee/               # لوحة تحكم الموظف
│   ├── dashboard.php
│   └── my_tasks.php
├── includes/               # ملفات النظام الأساسية
│   ├── auth.php
│   └── logout.php
├── config/                 # ملفات الإعداد
│   └── database.php
├── assets/                 # الملفات الثابتة
│   └── css/
│       └── style.css
├── sql/                    # ملفات قاعدة البيانات
│   └── database.sql
├── login.php               # صفحة تسجيل الدخول
├── index.php               # الصفحة الرئيسية
└── README.md
```

## الميزات المتقدمة

### نظام الإشعارات
- إشعارات فورية عند تكليف مهام جديدة
- تنبيهات للمواعيد النهائية
- إشعارات تحديث حالة المهام

### إدارة الملفات
- رفع المرفقات للمهام
- مشاركة الملفات بين أعضاء الفريق

### التقارير المتقدمة
- تقارير الأداء الشهرية
- إحصائيات الإنتاجية
- تحليل الأداء حسب القسم

## الأمان

- تشفير كلمات المرور باستخدام PHP password_hash()
- حماية من هجمات SQL Injection
- نظام جلسات آمن
- التحقق من الصلاحيات في كل صفحة

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:

- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXX-XXXX

## الترخيص

هذا المشروع مطور خصيصاً للشركة ومحمي بحقوق الطبع والنشر.

---

**تم التطوير بواسطة**: فريق تقنية المعلومات  
**تاريخ الإصدار**: 2024  
**الإصدار**: 1.0.0
